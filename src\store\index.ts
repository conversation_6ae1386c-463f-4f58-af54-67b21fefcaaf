import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import type { 
  AppConfig, 
  Message, 
  AgentState, 
  Conversation,
  ToolResult,
  AgentPlan,
  ExecutionMode
} from '../types';

// Main application store interface
interface AppStore {
  // Configuration
  config: AppConfig;
  updateConfig: (config: Partial<AppConfig>) => void;
  resetConfig: () => void;

  // UI State
  isSettingsOpen: boolean;
  isConfirmationModalOpen: boolean;
  currentPlan: AgentPlan | null;
  executionMode: ExecutionMode;
  theme: 'light' | 'dark';
  sidebarCollapsed: boolean;

  // Agent State
  agentState: AgentState;
  updateAgentState: (state: Partial<AgentState>) => void;

  // Messages and Conversations
  currentConversationId: string | null;
  conversations: Conversation[];
  messages: Message[];
  
  // Actions
  setSettingsOpen: (open: boolean) => void;
  setConfirmationModalOpen: (open: boolean) => void;
  setPendingPlan: (plan: AgentPlan | null) => void;
  setExecutionMode: (mode: ExecutionMode) => void;
  toggleTheme: () => void;
  toggleSidebar: () => void;
  
  // Message management
  addMessage: (message: Message) => void;
  updateMessage: (messageId: string, updates: Partial<Message>) => void;
  deleteMessage: (messageId: string) => void;
  clearMessages: () => void;
  
  // Conversation management
  createConversation: (title?: string) => string;
  loadConversation: (conversationId: string) => void;
  updateConversation: (conversationId: string, updates: Partial<Conversation>) => void;
  deleteConversation: (conversationId: string) => void;
  
  // Tool execution
  addToolResult: (result: ToolResult) => void;
  
  // Utility actions
  reset: () => void;
}

// Default configuration
const defaultConfig: AppConfig = {
  llm: {
    provider: 'openai',
    model: 'gpt-4',
    apiKey: '',
    baseUrl: '',
    temperature: 0.7,
    maxTokens: 4000,
    streamResponse: true
  },
  ui: {
    theme: 'dark',
    fontSize: 'medium',
    showLineNumbers: true,
    wordWrap: true,
    minimap: false
  },
  agent: {
    systemPrompt: 'You are Arien, an AI assistant that helps with coding and development tasks. You have access to various tools to read, write, and execute code. Always explain your reasoning and ask for confirmation before making destructive changes.',
    maxIterations: 10,
    confirmDestructiveActions: true,
    autoExecuteTools: false,
    contextWindow: 8000
  },
  tools: {
    enabledTools: ['read_file', 'write_file', 'list_files', 'grep', 'run_shell_command'],
    toolTimeout: 30000,
    maxConcurrentTools: 3
  }
};

const defaultAgentState: AgentState = {
  isProcessing: false,
  streamingResponse: false,
  executingTools: false,
  awaitingConfirmation: false,
  currentPlan: undefined,
  error: null,
  lastActivity: new Date()
};

// Create the store
export const useAppStore = create<AppStore>()(
  persist(
    immer((set, get) => ({
      // Configuration
      config: defaultConfig,
      updateConfig: (configUpdates) =>
        set((state) => {
          state.config = { ...state.config, ...configUpdates };
        }),
      resetConfig: () =>
        set((state) => {
          state.config = defaultConfig;
        }),

      // UI State
      isSettingsOpen: false,
      isConfirmationModalOpen: false,
      currentPlan: null,
      executionMode: 'confirm' as ExecutionMode,
      theme: 'dark',
      sidebarCollapsed: false,

      // Agent State
      agentState: defaultAgentState,
      updateAgentState: (stateUpdates) =>
        set((state) => {
          state.agentState = { ...state.agentState, ...stateUpdates };
        }),

      // Messages and Conversations
      currentConversationId: null,
      conversations: [],
      messages: [],

      // UI Actions
      setSettingsOpen: (open) =>
        set((state) => {
          state.isSettingsOpen = open;
        }),
      setConfirmationModalOpen: (open) =>
        set((state) => {
          state.isConfirmationModalOpen = open;
        }),
      setPendingPlan: (plan) =>
        set((state) => {
          state.currentPlan = plan;
        }),
      setExecutionMode: (mode) =>
        set((state) => {
          state.executionMode = mode;
        }),
      toggleTheme: () =>
        set((state) => {
          state.theme = state.theme === 'light' ? 'dark' : 'light';
          state.config.theme = state.theme;
        }),
      toggleSidebar: () =>
        set((state) => {
          state.sidebarCollapsed = !state.sidebarCollapsed;
        }),

      // Message management
      addMessage: (message) =>
        set((state) => {
          state.messages.push(message);
        }),
      updateMessage: (messageId, updates) =>
        set((state) => {
          const messageIndex = state.messages.findIndex(m => m.id === messageId);
          if (messageIndex !== -1) {
            state.messages[messageIndex] = { ...state.messages[messageIndex], ...updates };
          }
        }),
      deleteMessage: (messageId) =>
        set((state) => {
          state.messages = state.messages.filter(m => m.id !== messageId);
        }),
      clearMessages: () =>
        set((state) => {
          state.messages = [];
        }),

      // Conversation management
      createConversation: (title) => {
        const conversationId = `conv_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const conversation: Conversation = {
          id: conversationId,
          title: title || 'New Conversation',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          message_count: 0
        };
        
        set((state) => {
          state.conversations.unshift(conversation);
          state.currentConversationId = conversationId;
          state.messages = [];
        });
        
        return conversationId;
      },
      loadConversation: (conversationId) =>
        set((state) => {
          state.currentConversationId = conversationId;
          // Messages would be loaded from database in real implementation
          state.messages = [];
        }),
      updateConversation: (conversationId, updates) =>
        set((state) => {
          const conversationIndex = state.conversations.findIndex(c => c.id === conversationId);
          if (conversationIndex !== -1) {
            state.conversations[conversationIndex] = { 
              ...state.conversations[conversationIndex], 
              ...updates,
              updated_at: new Date().toISOString()
            };
          }
        }),
      deleteConversation: (conversationId) =>
        set((state) => {
          state.conversations = state.conversations.filter(c => c.id !== conversationId);
          if (state.currentConversationId === conversationId) {
            state.currentConversationId = null;
            state.messages = [];
          }
        }),

      // Tool execution
      addToolResult: (result) =>
        set((state) => {
          // Tool results are typically added as messages
          const toolMessage: Message = {
            id: result.id,
            type: 'tool',
            content: result.output,
            timestamp: new Date(),
            metadata: {
              success: result.success,
              error: result.error,
              executionTime: result.executionTime
            }
          };
          state.messages.push(toolMessage);
        }),

      // Utility actions
      reset: () =>
        set((state) => {
          state.config = defaultConfig;
          state.agentState = defaultAgentState;
          state.currentConversationId = null;
          state.conversations = [];
          state.messages = [];
          state.isSettingsOpen = false;
          state.isConfirmationModalOpen = false;
          state.currentPlan = null;
          state.executionMode = 'confirm';
          state.theme = 'dark';
          state.sidebarCollapsed = false;
        })
    })),
    {
      name: 'arien-agent-store',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        config: state.config,
        theme: state.theme,
        sidebarCollapsed: state.sidebarCollapsed,
        executionMode: state.executionMode,
        conversations: state.conversations
      })
    }
  )
);

// Selector hooks for better performance
export const useConfig = () => useAppStore((state) => state.config);
export const useAgentState = () => useAppStore((state) => state.agentState);
export const useMessages = () => useAppStore((state) => state.messages);
export const useConversations = () => useAppStore((state) => state.conversations);
export const useCurrentConversation = () => useAppStore((state) => 
  state.conversations.find(c => c.id === state.currentConversationId)
);
export const useUIState = () => useAppStore((state) => ({
  isSettingsOpen: state.isSettingsOpen,
  isConfirmationModalOpen: state.isConfirmationModalOpen,
  theme: state.theme,
  sidebarCollapsed: state.sidebarCollapsed,
  executionMode: state.executionMode
}));

// Action hooks
export const useAppActions = () => useAppStore((state) => ({
  updateConfig: state.updateConfig,
  setSettingsOpen: state.setSettingsOpen,
  setConfirmationModalOpen: state.setConfirmationModalOpen,
  setPendingPlan: state.setPendingPlan,
  setExecutionMode: state.setExecutionMode,
  toggleTheme: state.toggleTheme,
  toggleSidebar: state.toggleSidebar,
  addMessage: state.addMessage,
  updateMessage: state.updateMessage,
  deleteMessage: state.deleteMessage,
  clearMessages: state.clearMessages,
  createConversation: state.createConversation,
  loadConversation: state.loadConversation,
  updateConversation: state.updateConversation,
  deleteConversation: state.deleteConversation,
  addToolResult: state.addToolResult,
  reset: state.reset
}));
