import React, { useMemo } from 'react';
import { html } from 'diff2html';
import { createTwoFilesPatch } from 'diff';
import type { DiffData } from '../../types';

interface DiffViewerProps {
  diffData: DiffData;
  className?: string;
  showLineNumbers?: boolean;
  showFileHeader?: boolean;
  onAccept?: () => void;
  onReject?: () => void;
  onComment?: (lineNumber: number, comment: string) => void;
}

export const DiffViewer: React.FC<DiffViewerProps> = ({
  diffData,
  className = '',
  showLineNumbers = true,
  showFileHeader = true,
  onAccept,
  onReject,
  onComment
}) => {
  const diffHtml = useMemo(() => {
    try {
      // Create unified diff
      const patch = createTwoFilesPatch(
        diffData.fileName,
        diffData.fileName,
        diffData.oldContent,
        diffData.newContent,
        'Original',
        'Modified'
      );

      // Generate HTML diff
      const diffHtmlString = html(patch, {
        drawFileList: false,
        matching: 'lines',
        outputFormat: 'side-by-side',
        synchronisedScroll: true,
        highlight: true,
        fileListToggle: false,
        fileListStartVisible: false,
        fileContentToggle: false,
        maxLineLengthHighlight: 10000,
        maxLineSizeInBlockForComparison: 200,
        renderNothingWhenEmpty: false,
        compiledTemplates: {},
        rawTemplates: {
          'generic-wrapper': `<div class="d2h-wrapper">
            <div class="d2h-file-wrapper" data-lang="{{file.language}}">
              <div class="d2h-file-header">
                <span class="d2h-file-name-wrapper">
                  <span class="d2h-icon-wrapper"><span class="d2h-icon d2h-changed" title="modified"></span></span>
                  <span class="d2h-file-name">{{file.oldName}}</span>
                  <span class="d2h-tag d2h-changed d2h-changed-tag">MODIFIED</span>
                </span>
              </div>
              <div class="d2h-files-diff">
                <div class="d2h-file-side-diff">
                  <div class="d2h-code-wrapper">
                    <table class="d2h-diff-table">
                      <tbody class="d2h-diff-tbody">
                        {{{diffs}}}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>`
        }
      });

      return diffHtmlString;
    } catch (error) {
      console.error('Failed to generate diff:', error);
      return '<div class="diff-error">Failed to generate diff</div>';
    }
  }, [diffData]);

  const handleLineComment = (lineNumber: number) => {
    if (onComment) {
      const comment = prompt(`Add comment for line ${lineNumber}:`);
      if (comment) {
        onComment(lineNumber, comment);
      }
    }
  };

  return (
    <div className={`diff-viewer ${className}`}>
      {/* Header */}
      {showFileHeader && (
        <div className="diff-header bg-surface border border-border-primary rounded-t-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="flex items-center space-x-2">
                <svg className="w-5 h-5 text-text-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                <span className="font-medium text-text-primary">{diffData.fileName}</span>
              </div>
              <div className="px-2 py-1 bg-yellow-400/10 text-yellow-400 text-xs font-medium rounded border border-yellow-400/20">
                MODIFIED
              </div>
            </div>
            
            {/* Action buttons */}
            {(onAccept || onReject) && (
              <div className="flex items-center space-x-2">
                {onReject && (
                  <button
                    onClick={onReject}
                    className="px-3 py-1.5 text-sm bg-red-500/10 text-red-400 border border-red-500/20 rounded hover:bg-red-500/20 transition-colors"
                  >
                    Reject
                  </button>
                )}
                {onAccept && (
                  <button
                    onClick={onAccept}
                    className="px-3 py-1.5 text-sm bg-green-500/10 text-green-400 border border-green-500/20 rounded hover:bg-green-500/20 transition-colors"
                  >
                    Accept
                  </button>
                )}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Diff content */}
      <div 
        className="diff-content bg-primary border border-border-primary rounded-b-lg overflow-auto"
        dangerouslySetInnerHTML={{ __html: diffHtml }}
        style={{ maxHeight: '600px' }}
      />

      {/* Custom styles for diff2html */}
      <style>{`
        .diff-viewer :global(.d2h-wrapper) {
          font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
          font-size: 13px;
          line-height: 1.4;
        }

        .diff-viewer :global(.d2h-file-header) {
          background: var(--bg-tertiary);
          border-bottom: 1px solid var(--border-primary);
          padding: 8px 12px;
          color: var(--text-primary);
        }

        .diff-viewer :global(.d2h-file-name) {
          color: var(--text-primary);
          font-weight: 500;
        }

        .diff-viewer :global(.d2h-tag) {
          background: rgba(245, 158, 11, 0.1);
          color: #f59e0b;
          border: 1px solid rgba(245, 158, 11, 0.2);
          padding: 2px 6px;
          border-radius: 4px;
          font-size: 11px;
          font-weight: 500;
        }

        .diff-viewer :global(.d2h-diff-table) {
          width: 100%;
          border-collapse: collapse;
          background: var(--bg-primary);
        }

        .diff-viewer :global(.d2h-diff-tbody tr) {
          border: none;
        }

        .diff-viewer :global(.d2h-code-linenumber) {
          background: var(--bg-secondary);
          color: var(--text-tertiary);
          border-right: 1px solid var(--border-primary);
          padding: 2px 8px;
          text-align: right;
          user-select: none;
          width: 50px;
          min-width: 50px;
        }

        .diff-viewer :global(.d2h-code-side-line) {
          padding: 2px 8px;
          white-space: pre-wrap;
          word-break: break-all;
        }

        .diff-viewer :global(.d2h-del) {
          background: rgba(239, 68, 68, 0.1);
          color: var(--text-primary);
        }

        .diff-viewer :global(.d2h-del .d2h-code-linenumber) {
          background: rgba(239, 68, 68, 0.2);
          border-color: rgba(239, 68, 68, 0.3);
        }

        .diff-viewer :global(.d2h-ins) {
          background: rgba(34, 197, 94, 0.1);
          color: var(--text-primary);
        }

        .diff-viewer :global(.d2h-ins .d2h-code-linenumber) {
          background: rgba(34, 197, 94, 0.2);
          border-color: rgba(34, 197, 94, 0.3);
        }

        .diff-viewer :global(.d2h-cntx) {
          background: var(--bg-primary);
          color: var(--text-secondary);
        }

        .diff-viewer :global(.d2h-info) {
          background: var(--bg-tertiary);
          color: var(--text-accent);
          border-top: 1px solid var(--border-primary);
          border-bottom: 1px solid var(--border-primary);
        }

        .diff-viewer :global(.d2h-code-side-emptyplaceholder) {
          background: var(--bg-secondary);
          border-right: 1px solid var(--border-primary);
        }

        .diff-viewer :global(.hljs) {
          background: transparent;
          color: var(--text-primary);
        }

        .diff-viewer :global(.hljs-keyword) {
          color: #c678dd;
        }

        .diff-viewer :global(.hljs-string) {
          color: #98c379;
        }

        .diff-viewer :global(.hljs-comment) {
          color: var(--text-tertiary);
          font-style: italic;
        }

        .diff-viewer :global(.hljs-number) {
          color: #d19a66;
        }

        .diff-viewer :global(.hljs-function) {
          color: #61afef;
        }

        .diff-error {
          padding: 20px;
          text-align: center;
          color: var(--text-secondary);
          background: var(--bg-surface);
          border: 1px solid var(--border-primary);
          border-radius: 8px;
        }
      `}</style>
    </div>
  );
};

export default DiffViewer;
