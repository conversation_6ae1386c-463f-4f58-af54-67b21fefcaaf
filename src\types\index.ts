// Core Types for Arien Agent

export interface Message {
  id: string;
  type: 'user' | 'ai' | 'tool' | 'system';
  content: string;
  timestamp: Date;
  metadata?: {
    toolName?: string;
    toolArgs?: any;
    executionTime?: number;
    error?: string;
    streaming?: boolean;
    success?: boolean;
  };
}

export interface ToolCall {
  id: string;
  name: string;
  arguments: Record<string, any>;
}

export interface ToolResult {
  id: string;
  success: boolean;
  output: string;
  error?: string;
  executionTime: number;
}

export interface AgentPlan {
  id: string;
  description: string;
  toolCalls: ToolCall[];
  reasoning: string;
  requiresConfirmation: boolean;
}

export interface ToolSchema {
  name: string;
  description: string;
  parameters: {
    type: 'object';
    properties: Record<string, {
      type: string;
      description: string;
      enum?: string[];
      required?: boolean;
    }>;
    required: string[];
  };
  type?: string;
  properties?: Record<string, any>;
  required?: string[];
}

export interface Tool {
  name: string;
  description: string;
  schema: ToolSchema;
  execute: (args: Record<string, any>) => Promise<ToolResult>;
}

// LLM Provider Types
export interface LLMProvider {
  name: string;
  models: string[];
  generateResponse: (messages: Message[], tools?: ToolSchema[]) => Promise<string>;
  generatePlan: (prompt: string, context: Message[], tools: ToolSchema[]) => Promise<AgentPlan>;
  streamResponse: (messages: Message[], tools?: ToolSchema[]) => AsyncGenerator<string, void, unknown>;
  countTokens: (text: string) => number;
  getMaxTokens: (model: string) => number;
  getConfig: () => LLMConfig;
  getProviderName: () => string;
}

export interface LLMConfig {
  provider: 'openai' | 'anthropic' | 'deepseek';
  model: string;
  apiKey: string;
  baseUrl?: string;
  maxTokens?: number;
  temperature?: number;
  streamResponse?: boolean;
}

// Application State Types
export type ExecutionMode = 'confirm' | 'yolo';

export interface AppConfig {
  llm: LLMConfig;
  executionMode: ExecutionMode;
  autoSave: boolean;
  maxHistoryLength: number;
  theme: 'dark' | 'light';
  ui?: {
    theme: 'dark' | 'light';
    fontSize: 'small' | 'medium' | 'large';
    showLineNumbers: boolean;
    wordWrap: boolean;
    minimap: boolean;
  };
  agent?: {
    systemPrompt: string;
    maxIterations: number;
    confirmBeforeExecution: boolean;
    confirmDestructiveActions: boolean;
    autoExecuteTools: boolean;
    contextWindow: number;
  };
  tools?: {
    enabled: string[];
    disabled: string[];
  };
}

export interface Conversation {
  id: string;
  title: string;
  created_at: string;
  updated_at: string;
  message_count: number;
}

export interface AgentState {
  isProcessing: boolean;
  currentPlan?: AgentPlan;
  awaitingConfirmation: boolean;
  executingTools: boolean;
  streamingResponse: boolean;
  error?: string | null;
  lastActivity?: Date;
}

export interface DatabaseSchema {
  conversations: {
    id: string;
    title: string;
    created_at: string;
    updated_at: string;
    message_count: number;
  };
  messages: {
    id: string;
    conversation_id: string;
    type: string;
    content: string;
    timestamp: string;
    metadata: string; // JSON
  };
  tool_executions: {
    id: string;
    message_id: string;
    tool_name: string;
    arguments: string; // JSON
    result: string; // JSON
    execution_time: number;
    created_at: string;
  };
  context_cache: {
    id: string;
    key: string;
    value: string; // JSON
    expires_at: string;
    created_at: string;
  };
}

// UI Component Types
export interface BaseModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
}

export interface ModalProps extends BaseModalProps {
  children: React.ReactNode;
}

export interface ConfirmationModalProps extends BaseModalProps {
  plan: AgentPlan;
  onConfirm: () => void;
  onCancel: () => void;
}

export interface SettingsModalProps extends BaseModalProps {
  config: AppConfig;
  onSave: (config: AppConfig) => void;
}

// Keyboard Shortcut Types
export interface KeyboardShortcut {
  key: string;
  description: string;
  action: () => void;
  global?: boolean;
}

// File System Types
export interface FileInfo {
  path: string;
  name: string;
  size: number;
  isDirectory: boolean;
  lastModified: Date;
}

export interface DirectoryListing {
  path: string;
  files: FileInfo[];
  directories: FileInfo[];
}

// Diff Viewer Types
export interface DiffData {
  oldContent: string;
  newContent: string;
  fileName: string;
  language?: string;
}

// Error Types
export interface AppError {
  code: string;
  message: string;
  details?: any;
  timestamp: Date;
}

// IPC Types for Electron
export interface IPCMessage {
  channel: string;
  data: any;
}

export interface ToolExecutionRequest {
  toolName: string;
  arguments: Record<string, any>;
  requestId: string;
}

export interface ToolExecutionResponse {
  requestId: string;
  success: boolean;
  result: any;
  error?: string;
}

// Streaming Types
export interface StreamChunk {
  type: 'content' | 'tool_call' | 'error' | 'done';
  data: any;
}

// Context Management Types
export interface ConversationContext {
  messages: Message[];
  tokenCount: number;
  summary?: string;
  lastUpdated: Date;
}

export interface ContextWindow {
  maxTokens: number;
  currentTokens: number;
  messages: Message[];
  systemPrompt: string;
  toolSchemas: ToolSchema[];
}
