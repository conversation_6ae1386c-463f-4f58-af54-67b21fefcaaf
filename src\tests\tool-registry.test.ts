import type { Tool } from '../types';

// Mock the ToolRegistry class for testing
class MockToolRegistry {
  private tools = new Map<string, Tool>();

  registerTool(tool: Tool): void {
    if (this.tools.has(tool.name)) {
      throw new Error(`Tool ${tool.name} is already registered`);
    }
    this.tools.set(tool.name, tool);
  }

  getToolNames(): string[] {
    return Array.from(this.tools.keys());
  }

  getToolInfo(name: string): any {
    const tool = this.tools.get(name);
    return tool ? {
      name: tool.name,
      description: tool.description,
      schema: tool.schema
    } : null;
  }

  async executeTool(name: string, args: any): Promise<any> {
    const tool = this.tools.get(name);
    if (!tool) {
      return {
        success: false,
        error: `Tool ${name} not found`
      };
    }

    // Simple validation
    if (tool.schema?.required) {
      for (const required of tool.schema.required) {
        if (!(required in args)) {
          return {
            success: false,
            error: `Invalid arguments: Missing required parameter: ${required}`
          };
        }
      }
    }

    try {
      return await tool.execute(args);
    } catch (error: any) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  validateArguments(schema: any, args: any): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (schema.required) {
      for (const required of schema.required) {
        if (!(required in args)) {
          errors.push(`Missing required parameter: ${required}`);
        }
      }
    }

    if (schema.properties) {
      for (const [key, value] of Object.entries(args)) {
        const propSchema = schema.properties[key];
        if (!propSchema) continue;

        const expectedType = (propSchema as any).type;
        const actualType = typeof value;

        if (expectedType === 'string' && actualType !== 'string') {
          errors.push(`Parameter '${key}' must be a string`);
        } else if (expectedType === 'number' && actualType !== 'number') {
          errors.push(`Parameter '${key}' must be a number`);
        } else if (expectedType === 'boolean' && actualType !== 'boolean') {
          errors.push(`Parameter '${key}' must be a boolean`);
        } else if (expectedType === 'array' && !Array.isArray(value)) {
          errors.push(`Parameter '${key}' must be an array`);
        }
      }
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  getToolSchemas(): Record<string, any> {
    const schemas: Record<string, any> = {};
    for (const [name, tool] of this.tools) {
      schemas[name] = tool.schema;
    }
    return schemas;
  }

  clearTools(): void {
    this.tools.clear();
  }
}

describe('ToolRegistry', () => {
  let registry: MockToolRegistry;

  beforeEach(() => {
    registry = new MockToolRegistry();
  });

  describe('Tool Registration', () => {
    test('should register a tool', () => {
      const mockTool = {
        name: 'test-tool',
        description: 'A test tool',
        schema: {
          type: 'object',
          properties: {
            input: { type: 'string' }
          },
          required: ['input']
        },
        execute: async (args: any) => ({
          id: 'test-1',
          success: true,
          output: `Processed: ${args.input}`,
          executionTime: 100
        })
      };

      registry.registerTool(mockTool);

      const toolNames = registry.getToolNames();
      expect(toolNames).toContain('test-tool');
    });

    test('should prevent duplicate tool registration', () => {
      const mockTool: Tool = {
        name: 'duplicate-tool',
        description: 'A duplicate tool',
        schema: { type: 'object', properties: {} },
        execute: async () => ({ id: '1', success: true, output: '', executionTime: 0 })
      };

      registry.registerTool(mockTool);
      
      expect(() => registry.registerTool(mockTool)).toThrow('Tool duplicate-tool is already registered');
    });

    test('should get tool information', () => {
      const mockTool: Tool = {
        name: 'info-tool',
        description: 'Tool for testing info retrieval',
        schema: {
          type: 'object',
          properties: {
            param: { type: 'string', description: 'A parameter' }
          }
        },
        execute: async () => ({ id: '1', success: true, output: '', executionTime: 0 })
      };

      registry.registerTool(mockTool);
      
      const info = registry.getToolInfo('info-tool');
      expect(info).toBeDefined();
      expect(info?.name).toBe('info-tool');
      expect(info?.description).toBe('Tool for testing info retrieval');
    });
  });

  describe('Tool Execution', () => {
    beforeEach(() => {
      const mockTool: Tool = {
        name: 'echo-tool',
        description: 'Echoes input',
        schema: {
          type: 'object',
          properties: {
            message: { type: 'string' }
          },
          required: ['message']
        },
        execute: async (args) => ({
          id: `echo-${Date.now()}`,
          success: true,
          output: `Echo: ${args.message}`,
          executionTime: 50
        })
      };

      registry.registerTool(mockTool);
    });

    test('should execute tool with valid arguments', async () => {
      const result = await registry.executeTool('echo-tool', { message: 'Hello, World!' });
      
      expect(result.success).toBe(true);
      expect(result.output).toBe('Echo: Hello, World!');
      expect(result.executionTime).toBeGreaterThan(0);
    });

    test('should validate arguments against schema', async () => {
      const result = await registry.executeTool('echo-tool', {}); // Missing required 'message'
      
      expect(result.success).toBe(false);
      expect(result.error).toContain('Invalid arguments');
    });

    test('should handle tool execution errors', async () => {
      const errorTool: Tool = {
        name: 'error-tool',
        description: 'Always throws an error',
        schema: { type: 'object', properties: {} },
        execute: async () => {
          throw new Error('Tool execution failed');
        }
      };

      registry.registerTool(errorTool);
      
      const result = await registry.executeTool('error-tool', {});
      expect(result.success).toBe(false);
      expect(result.error).toBe('Tool execution failed');
    });

    test('should handle non-existent tool', async () => {
      const result = await registry.executeTool('non-existent-tool', {});
      
      expect(result.success).toBe(false);
      expect(result.error).toBe('Tool non-existent-tool not found');
    });
  });

  describe('Schema Validation', () => {
    test('should validate simple string parameter', () => {
      const schema = {
        type: 'object' as const,
        properties: {
          name: { type: 'string' as const }
        },
        required: ['name']
      };

      const validArgs = { name: 'John' };
      const invalidArgs = { name: 123 };

      const validResult = registry.validateArguments(schema, validArgs);
      expect(validResult.valid).toBe(true);

      const invalidResult = registry.validateArguments(schema, invalidArgs);
      expect(invalidResult.valid).toBe(false);
    });

    test('should validate number parameter', () => {
      const schema = {
        type: 'object' as const,
        properties: {
          count: { type: 'number' as const }
        },
        required: ['count']
      };

      const validArgs = { count: 42 };
      const invalidArgs = { count: 'not-a-number' };

      const validResult = registry.validateArguments(schema, validArgs);
      expect(validResult.valid).toBe(true);

      const invalidResult = registry.validateArguments(schema, invalidArgs);
      expect(invalidResult.valid).toBe(false);
    });

    test('should validate boolean parameter', () => {
      const schema = {
        type: 'object' as const,
        properties: {
          enabled: { type: 'boolean' as const }
        },
        required: ['enabled']
      };

      const validArgs = { enabled: true };
      const invalidArgs = { enabled: 'yes' };

      const validResult = registry.validateArguments(schema, validArgs);
      expect(validResult.valid).toBe(true);

      const invalidResult = registry.validateArguments(schema, invalidArgs);
      expect(invalidResult.valid).toBe(false);
    });

    test('should validate array parameter', () => {
      const schema = {
        type: 'object' as const,
        properties: {
          items: {
            type: 'array' as const,
            items: { type: 'string' as const }
          }
        },
        required: ['items']
      };

      const validArgs = { items: ['a', 'b', 'c'] };
      const invalidArgs = { items: 'not-an-array' };

      const validResult = registry.validateArguments(schema, validArgs);
      expect(validResult.valid).toBe(true);

      const invalidResult = registry.validateArguments(schema, invalidArgs);
      expect(invalidResult.valid).toBe(false);
    });

    test('should handle missing required parameters', () => {
      const schema = {
        type: 'object' as const,
        properties: {
          required_param: { type: 'string' as const },
          optional_param: { type: 'string' as const }
        },
        required: ['required_param']
      };

      const validArgs = { required_param: 'value' };
      const invalidArgs = { optional_param: 'value' }; // Missing required_param

      const validResult = registry.validateArguments(schema, validArgs);
      expect(validResult.valid).toBe(true);

      const invalidResult = registry.validateArguments(schema, invalidArgs);
      expect(invalidResult.valid).toBe(false);
      expect(invalidResult.errors).toContain('Missing required parameter: required_param');
    });
  });

  describe('Tool Management', () => {
    test('should get all tool schemas', () => {
      const tool1: Tool = {
        name: 'tool1',
        description: 'First tool',
        schema: { type: 'object', properties: { param1: { type: 'string' } } },
        execute: async () => ({ id: '1', success: true, output: '', executionTime: 0 })
      };

      const tool2: Tool = {
        name: 'tool2',
        description: 'Second tool',
        schema: { type: 'object', properties: { param2: { type: 'number' } } },
        execute: async () => ({ id: '2', success: true, output: '', executionTime: 0 })
      };

      registry.registerTool(tool1);
      registry.registerTool(tool2);

      const schemas = registry.getToolSchemas();
      expect(schemas).toHaveProperty('tool1');
      expect(schemas).toHaveProperty('tool2');
      expect(schemas.tool1.properties).toHaveProperty('param1');
      expect(schemas.tool2.properties).toHaveProperty('param2');
    });

    test('should clear all tools', () => {
      const mockTool: Tool = {
        name: 'temp-tool',
        description: 'Temporary tool',
        schema: { type: 'object', properties: {} },
        execute: async () => ({ id: '1', success: true, output: '', executionTime: 0 })
      };

      registry.registerTool(mockTool);
      expect(registry.getToolNames()).toContain('temp-tool');

      registry.clearTools();
      expect(registry.getToolNames()).not.toContain('temp-tool');
      expect(registry.getToolNames()).toHaveLength(0);
    });
  });
});
