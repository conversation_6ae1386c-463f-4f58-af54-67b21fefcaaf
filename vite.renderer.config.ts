import { defineConfig } from 'vite';
import wasm from 'vite-plugin-wasm';
import topLevelAwait from 'vite-plugin-top-level-await';
import { builtinModules } from 'module';

// https://vitejs.dev/config
export default defineConfig(async () => {
  const react = await import('@vitejs/plugin-react');

  return {
    plugins: [
      react.default(),
      wasm(),
      topLevelAwait()
    ],
    esbuild: {
      jsx: 'automatic',
    },
    css: {
      postcss: './postcss.config.js',
    },
    build: {
      rollupOptions: {
        external: [
          'electron',
          'fs',
          'fs/promises',
          'path',
          'child_process',
          'util',
          'better-sqlite3',
          ...builtinModules.flatMap((p) => [p, `node:${p}`]),
        ],
      },
    },
  };
});
