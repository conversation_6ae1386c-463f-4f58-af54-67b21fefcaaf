import { EventEmitter } from 'events';
import { v4 as uuidv4 } from 'uuid';
import { LLMFactory } from './llm/llm-factory';
import type { 
  Message, 
  AgentPlan, 
  ToolCall, 
  ToolResult, 
  LLMConfig, 
  AgentState,
  AppConfig 
} from '../types';

export interface AgentLoopEvents {
  'state-changed': (state: AgentState) => void;
  'message-added': (message: Message) => void;
  'plan-generated': (plan: AgentPlan) => void;
  'confirmation-required': (plan: AgentPlan) => void;
  'tool-execution-started': (toolCall: ToolCall) => void;
  'tool-execution-completed': (toolCall: ToolCall, result: ToolResult) => void;
  'response-streaming': (chunk: string) => void;
  'response-completed': (message: Message) => void;
  'error': (error: Error) => void;
}

export class AgentLoop extends EventEmitter {
  private state: AgentState;
  private config: AppConfig;
  private conversationId: string;
  private messages: Message[] = [];

  constructor(config: AppConfig, conversationId?: string) {
    super();

    this.config = config;
    this.conversationId = conversationId || this.createNewConversation();
    
    this.state = {
      isProcessing: false,
      awaitingConfirmation: false,
      executingTools: false,
      streamingResponse: false
    };

    this.initializeTools();
  }

  private async initializeTools(): Promise<void> {
    // Tools are initialized in the main process
    // No action needed in renderer
  }

  private createNewConversation(): string {
    // Generate a UUID for the conversation
    // The actual database creation will be handled via IPC when needed
    return uuidv4();
  }

  public async processPrompt(prompt: string): Promise<void> {
    try {
      this.updateState({ isProcessing: true });

      // Step 1: Listen - Add user message
      const userMessage = this.createMessage('user', prompt);
      this.addMessage(userMessage);

      // Step 2: Understand & Plan
      const plan = await this.generatePlan(prompt);
      this.emit('plan-generated', plan);

      // Step 3: Confirm (if required)
      if (plan.requiresConfirmation && this.config.executionMode === 'confirm') {
        this.updateState({ awaitingConfirmation: true });
        this.emit('confirmation-required', plan);
        return; // Wait for user confirmation
      }

      // Step 4: Execute
      await this.executePlan(plan);

    } catch (error) {
      this.handleError(error as Error);
    } finally {
      this.updateState({ 
        isProcessing: false, 
        awaitingConfirmation: false,
        executingTools: false,
        streamingResponse: false
      });
    }
  }

  public async confirmPlan(plan: AgentPlan): Promise<void> {
    try {
      this.updateState({ awaitingConfirmation: false });
      await this.executePlan(plan);
    } catch (error) {
      this.handleError(error as Error);
    }
  }

  public cancelPlan(): void {
    this.updateState({ 
      awaitingConfirmation: false,
      isProcessing: false 
    });
    
    const cancelMessage = this.createMessage('ai', 'Plan cancelled by user.');
    this.addMessage(cancelMessage);
  }

  private async generatePlan(prompt: string): Promise<AgentPlan> {
    const provider = LLMFactory.getProvider(this.config.llm);

    // Get tool schemas via IPC
    const tools = await (window as any).electronAPI?.getToolSchemas?.() || [];

    return await provider.generatePlan(prompt, this.messages, tools);
  }

  private async executePlan(plan: AgentPlan): Promise<void> {
    this.updateState({ executingTools: true, currentPlan: plan });

    const toolResults: ToolResult[] = [];

    // Execute each tool call
    for (const toolCall of plan.toolCalls) {
      this.emit('tool-execution-started', toolCall);

      // Execute tool via IPC
      const result = await (window as any).electronAPI?.executeTool?.({
        requestId: uuidv4(),
        toolName: toolCall.name,
        arguments: toolCall.arguments
      });

      if (result) {
        toolResults.push({
          id: result.requestId,
          success: result.success,
          output: result.result?.output || '',
          error: result.error || result.result?.error,
          executionTime: result.result?.executionTime || 0
        });
      }

      // Tool execution is saved to database in main process
      const toolMessage = this.createMessage('tool', JSON.stringify(result), {
        toolName: toolCall.name,
        toolArgs: toolCall.arguments,
        executionTime: result.executionTime,
        error: result.error
      });
      this.addMessage(toolMessage);
      
      this.emit('tool-execution-completed', toolCall, result);
      
      // Stop execution if a tool failed and it's critical
      if (!result.success && this.isToolCritical(toolCall.name)) {
        break;
      }
    }

    this.updateState({ executingTools: false });

    // Step 5: Observe & Respond
    await this.generateResponse(plan, toolResults);
  }

  private async generateResponse(plan: AgentPlan, toolResults: ToolResult[]): Promise<void> {
    this.updateState({ streamingResponse: true });

    const provider = LLMFactory.getProvider(this.config.llm);
    const tools = await (window as any).electronAPI?.getToolSchemas?.() || [];

    // Add tool results to context
    const contextMessages = [...this.messages];
    
    // Add plan context
    const planMessage = this.createMessage('system', 
      `Plan executed: ${plan.description}\nReasoning: ${plan.reasoning}\nTool results: ${JSON.stringify(toolResults, null, 2)}`
    );
    contextMessages.push(planMessage);

    try {
      // Stream the response
      const responseChunks: string[] = [];
      const responseStream = provider.streamResponse(contextMessages, tools);

      for await (const chunk of responseStream) {
        responseChunks.push(chunk);
        this.emit('response-streaming', chunk);
      }

      const fullResponse = responseChunks.join('');
      const responseMessage = this.createMessage('ai', fullResponse);
      this.addMessage(responseMessage);
      
      this.emit('response-completed', responseMessage);
    } catch (error) {
      // Fallback to non-streaming response
      const response = await provider.generateResponse(contextMessages, tools);
      const responseMessage = this.createMessage('ai', response);
      this.addMessage(responseMessage);
      
      this.emit('response-completed', responseMessage);
    } finally {
      this.updateState({ streamingResponse: false });
    }
  }

  private createMessage(type: Message['type'], content: string, metadata?: Message['metadata']): Message {
    return {
      id: uuidv4(),
      type,
      content,
      timestamp: new Date(),
      metadata
    };
  }

  private addMessage(message: Message): void {
    this.messages.push(message);
    // Save message via IPC (fire and forget)
    (window as any).electronAPI?.saveMessage?.(message, this.conversationId);
    this.emit('message-added', message);
  }

  private updateState(updates: Partial<AgentState>): void {
    this.state = { ...this.state, ...updates };
    this.emit('state-changed', this.state);
  }

  private isToolCritical(toolName: string): boolean {
    // Define which tools are critical - if they fail, stop execution
    const criticalTools = ['write_file', 'delete_file', 'run_shell_command'];
    return criticalTools.includes(toolName);
  }

  private handleError(error: Error): void {
    console.error('Agent loop error:', error);
    
    const errorMessage = this.createMessage('ai', 
      `I encountered an error: ${error.message}. Please try again or rephrase your request.`
    );
    this.addMessage(errorMessage);
    
    this.emit('error', error);
  }

  // Public getters
  public getState(): AgentState {
    return { ...this.state };
  }

  public getMessages(): Message[] {
    return [...this.messages];
  }

  public getConversationId(): string {
    return this.conversationId;
  }

  public getConfig(): AppConfig {
    return { ...this.config };
  }

  // Configuration updates
  public updateConfig(config: Partial<AppConfig>): void {
    this.config = { ...this.config, ...config };
  }

  public updateLLMConfig(llmConfig: Partial<LLMConfig>): void {
    this.config.llm = { ...this.config.llm, ...llmConfig };
  }

  // Message management
  public async loadConversation(conversationId: string): Promise<void> {
    this.conversationId = conversationId;
    // Load messages via IPC
    this.messages = await (window as any).electronAPI?.getMessages?.(conversationId) || [];
  }

  public clearMessages(): void {
    this.messages = [];
  }

  public async deleteMessage(messageId: string): Promise<void> {
    this.messages = this.messages.filter(m => m.id !== messageId);
    // Delete message via IPC
    (window as any).electronAPI?.deleteMessage?.(messageId);
  }

  // Tool management
  public async getAvailableTools(): Promise<string[]> {
    const tools = await (window as any).electronAPI?.getToolSchemas?.() || [];
    return tools.map((tool: any) => tool.name);
  }

  public async getToolInfo(toolName: string) {
    return await (window as any).electronAPI?.getToolInfo?.(toolName);
  }

  // Cleanup
  public destroy(): void {
    this.removeAllListeners();
    this.messages = [];
  }
}
