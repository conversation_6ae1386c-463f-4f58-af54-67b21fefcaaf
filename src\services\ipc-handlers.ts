import { ipc<PERSON><PERSON>, <PERSON><PERSON>erWindow } from 'electron';
import { readFile, writeFile, readdir, stat, unlink, rmdir } from 'fs/promises';
import { exec } from 'child_process';
import { promisify } from 'util';
import { join, dirname } from 'path';
import { ToolRegistry } from '../tools/tool-registry';
import { DatabaseManager } from '../database/database';
import type { ToolExecutionRequest, ToolExecutionResponse } from '../types';

const execAsync = promisify(exec);

export function setupIPC(): void {
  const toolRegistry = ToolRegistry.getInstance();
  const database = DatabaseManager.getInstance();

  // Initialize tools
  toolRegistry.loadTools();

  // Tool execution handler
  ipcMain.handle('execute-tool', async (event, request: ToolExecutionRequest): Promise<ToolExecutionResponse> => {
    try {
      const result = await toolRegistry.executeTool(request.toolName, request.arguments);
      
      return {
        requestId: request.requestId,
        success: result.success,
        result: {
          output: result.output,
          error: result.error,
          executionTime: result.executionTime
        }
      };
    } catch (error: any) {
      return {
        requestId: request.requestId,
        success: false,
        result: null,
        error: error.message || 'Tool execution failed'
      };
    }
  });

  // Tool management
  ipcMain.handle('tools-get-schemas', async (event) => {
    try {
      const toolRegistry = ToolRegistry.getInstance();
      return toolRegistry.getToolSchemas();
    } catch (error: any) {
      console.error('Failed to get tool schemas:', error);
      return [];
    }
  });

  ipcMain.handle('tools-get-info', async (event, toolName: string) => {
    try {
      const toolRegistry = ToolRegistry.getInstance();
      return toolRegistry.getToolInfo(toolName);
    } catch (error: any) {
      console.error('Failed to get tool info:', error);
      return null;
    }
  });

  // File system operations
  ipcMain.handle('fs-read-file', async (event, filePath: string, encoding: string = 'utf8') => {
    try {
      return await readFile(filePath, encoding as BufferEncoding);
    } catch (error: any) {
      throw new Error(`Failed to read file: ${error.message}`);
    }
  });

  ipcMain.handle('fs-write-file', async (event, filePath: string, content: string, encoding: string = 'utf8') => {
    try {
      // Ensure directory exists
      const dir = dirname(filePath);
      await import('fs/promises').then(fs => fs.mkdir(dir, { recursive: true }));
      
      await writeFile(filePath, content, encoding as BufferEncoding);
    } catch (error: any) {
      throw new Error(`Failed to write file: ${error.message}`);
    }
  });

  ipcMain.handle('fs-list-directory', async (event, dirPath: string, options: any = {}) => {
    try {
      const items = await readdir(dirPath);
      const results = [];

      for (const item of items) {
        const itemPath = join(dirPath, item);
        const stats = await stat(itemPath);
        
        results.push({
          name: item,
          path: itemPath,
          isDirectory: stats.isDirectory(),
          size: stats.size,
          lastModified: stats.mtime
        });
      }

      return results;
    } catch (error: any) {
      throw new Error(`Failed to list directory: ${error.message}`);
    }
  });

  ipcMain.handle('fs-delete-file', async (event, filePath: string, options: any = {}) => {
    try {
      const stats = await stat(filePath);
      
      if (stats.isDirectory()) {
        await rmdir(filePath, { recursive: options.recursive || false });
      } else {
        await unlink(filePath);
      }
    } catch (error: any) {
      throw new Error(`Failed to delete: ${error.message}`);
    }
  });

  // Shell command execution
  ipcMain.handle('shell-execute', async (event, command: string, options: any = {}) => {
    try {
      const { stdout, stderr } = await execAsync(command, {
        cwd: options.cwd || process.cwd(),
        timeout: options.timeout || 30000,
        env: { ...process.env, ...options.env }
      });

      return {
        success: true,
        stdout,
        stderr,
        exitCode: 0
      };
    } catch (error: any) {
      return {
        success: false,
        stdout: error.stdout || '',
        stderr: error.stderr || '',
        exitCode: error.code || 1,
        error: error.message
      };
    }
  });

  // System information
  ipcMain.handle('get-system-info', async () => {
    return {
      platform: process.platform,
      arch: process.arch,
      nodeVersion: process.version,
      electronVersion: process.versions.electron,
      chromeVersion: process.versions.chrome,
      cwd: process.cwd(),
      homedir: require('os').homedir(),
      tmpdir: require('os').tmpdir()
    };
  });

  // Settings management
  ipcMain.handle('get-settings', async () => {
    try {
      return database.getAllSettings();
    } catch (error: any) {
      throw new Error(`Failed to get settings: ${error.message}`);
    }
  });

  ipcMain.handle('save-settings', async (event, settings: Record<string, any>) => {
    try {
      for (const [key, value] of Object.entries(settings)) {
        database.saveSetting(key, value);
      }
    } catch (error: any) {
      throw new Error(`Failed to save settings: ${error.message}`);
    }
  });

  // Window controls
  ipcMain.on('window-minimize', (event) => {
    const window = BrowserWindow.fromWebContents(event.sender);
    window?.minimize();
  });

  ipcMain.on('window-maximize', (event) => {
    const window = BrowserWindow.fromWebContents(event.sender);
    if (window?.isMaximized()) {
      window.unmaximize();
    } else {
      window?.maximize();
    }
  });

  ipcMain.on('window-close', (event) => {
    const window = BrowserWindow.fromWebContents(event.sender);
    window?.close();
  });

  // Database operations
  ipcMain.handle('db-create-conversation', async (event, title: string) => {
    try {
      return database.createConversation(title);
    } catch (error: any) {
      throw new Error(`Failed to create conversation: ${error.message}`);
    }
  });

  ipcMain.handle('db-get-conversations', async () => {
    try {
      return database.getConversations();
    } catch (error: any) {
      throw new Error(`Failed to get conversations: ${error.message}`);
    }
  });

  ipcMain.handle('db-get-messages', async (event, conversationId: string) => {
    try {
      return database.getMessages(conversationId);
    } catch (error: any) {
      throw new Error(`Failed to get messages: ${error.message}`);
    }
  });

  ipcMain.handle('db-save-message', async (event, message: any, conversationId: string) => {
    try {
      database.saveMessage(message, conversationId);
    } catch (error: any) {
      throw new Error(`Failed to save message: ${error.message}`);
    }
  });

  ipcMain.handle('db-delete-conversation', async (event, conversationId: string) => {
    try {
      database.deleteConversation(conversationId);
    } catch (error: any) {
      throw new Error(`Failed to delete conversation: ${error.message}`);
    }
  });

  // Tool registry operations
  ipcMain.handle('tools-get-available', async () => {
    try {
      return toolRegistry.getToolNames();
    } catch (error: any) {
      throw new Error(`Failed to get available tools: ${error.message}`);
    }
  });

  ipcMain.handle('tools-get-schemas', async () => {
    try {
      return toolRegistry.getToolSchemas();
    } catch (error: any) {
      throw new Error(`Failed to get tool schemas: ${error.message}`);
    }
  });

  ipcMain.handle('tools-get-info', async (event, toolName: string) => {
    try {
      return toolRegistry.getToolInfo(toolName);
    } catch (error: any) {
      throw new Error(`Failed to get tool info: ${error.message}`);
    }
  });

  // Message management
  ipcMain.handle('save-message', async (event, message: any, conversationId: string) => {
    try {
      const database = DatabaseManager.getInstance();
      database.saveMessage(message, conversationId);
    } catch (error: any) {
      console.error('Failed to save message:', error);
    }
  });

  ipcMain.handle('get-messages', async (event, conversationId: string) => {
    try {
      const database = DatabaseManager.getInstance();
      return database.getMessages(conversationId);
    } catch (error: any) {
      console.error('Failed to get messages:', error);
      return [];
    }
  });

  ipcMain.handle('delete-message', async (event, messageId: string) => {
    try {
      const database = DatabaseManager.getInstance();
      database.deleteMessage(messageId);
    } catch (error: any) {
      console.error('Failed to delete message:', error);
    }
  });

  // Error handling
  process.on('uncaughtException', (error) => {
    console.error('Uncaught Exception:', error);
    // Send error to renderer if needed
    const windows = BrowserWindow.getAllWindows();
    windows.forEach(window => {
      window.webContents.send('main-process-error', {
        type: 'uncaughtException',
        message: error.message,
        stack: error.stack
      });
    });
  });

  process.on('unhandledRejection', (reason, promise) => {
    console.error('Unhandled Rejection at:', promise, 'reason:', reason);
    // Send error to renderer if needed
    const windows = BrowserWindow.getAllWindows();
    windows.forEach(window => {
      window.webContents.send('main-process-error', {
        type: 'unhandledRejection',
        message: String(reason),
        promise: String(promise)
      });
    });
  });

  console.log('IPC handlers set up successfully');
}
